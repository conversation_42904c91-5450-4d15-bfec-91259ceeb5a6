/**
 * Disable Analytics for Testing
 * This script disables all analytics and beacon API calls for testing purposes
 */

// Override navigator.sendBeacon to prevent beacon API calls
if (typeof navigator !== 'undefined' && navigator.sendBeacon) {
    const originalSendBeacon = navigator.sendBeacon.bind(navigator);
    
    navigator.sendBeacon = function(url, data) {
        console.log('🚫 Beacon API call blocked for testing:', url);
        return true; // Return true to indicate "success" without actually sending
    };
    
    console.log('📊 Analytics disabled for testing environment');
}

// Disable console errors for missing analytics endpoints
const originalFetch = window.fetch;
window.fetch = function(url, options) {
    if (typeof url === 'string' && (url.includes('/analytics/') || url.includes('/api/analytics/'))) {
        console.log('🚫 Analytics fetch blocked for testing:', url);
        return Promise.resolve(new Response('{"status":"disabled"}', {
            status: 200,
            statusText: 'OK',
            headers: { 'Content-Type': 'application/json' }
        }));
    }
    return originalFetch.apply(this, arguments);
};

// Prevent 404.html redirect loops
const originalBeaconAPI = navigator.sendBeacon;
if (originalBeaconAPI) {
    navigator.sendBeacon = function(url, data) {
        if (url && (url.includes('404.html') || url.includes('test.x-zoneservers.com'))) {
            console.log('🚫 Blocked problematic beacon call:', url);
            return true;
        }
        return originalBeaconAPI.call(this, url, data);
    };
}

// Disable service worker for testing to prevent caching conflicts
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
        for(let registration of registrations) {
            registration.unregister().then(function(boolean) {
                console.log('🔧 Service worker unregistered for testing');
            });
        }
    });
}

// Force reload scripts to bypass cache
const scripts = document.querySelectorAll('script[src*="js/"]');
scripts.forEach(script => {
    if (script.src.includes('js/')) {
        const newScript = document.createElement('script');
        newScript.src = script.src + '?v=' + Date.now();
        newScript.async = script.async;
        script.parentNode.replaceChild(newScript, script);
    }
});

console.log('✅ Analytics and beacon API calls disabled for testing');
