/* Critical CSS for above-the-fold content - Optimized for Core Web Vitals */

/* Base styles to prevent layout shift */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #020617; /* slate-950 */
    color: #d1d5db; /* gray-300 */
    margin: 0;
    padding: 0;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
}

/* Header placeholder to prevent layout shift */
.header-placeholder, #header-placeholder {
    height: 80px;
    background-color: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
}

/* Hero section critical styles */
.hero-gradient {
    background: radial-gradient(ellipse at top, rgba(14, 165, 233, 0.15), transparent 60%);
}

/* Button styles for immediate interactivity */
.btn-primary {
    background: linear-gradient(135deg, #0ea5e9, #3b82f6);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    border: none;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.08s ease-out, background-color 0.12s ease-out;
    will-change: transform;
    cursor: pointer;
}

.btn-secondary {
    background: rgba(51, 65, 85, 0.8);
    color: #e2e8f0;
    border: 1px solid rgba(148, 163, 184, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.08s ease-out, background-color 0.12s ease-out;
    will-change: transform;
    cursor: pointer;
}

.btn-primary:hover, .btn-secondary:hover {
    transform: translateY(-1px);
}

.btn-primary:active, .btn-secondary:active {
    transform: scale(0.98);
}

/* Focus styles for accessibility */
.btn-primary:focus-visible, .btn-secondary:focus-visible {
    outline: 2px solid #0ea5e9;
    outline-offset: 2px;
}

/* Image optimization for LCP */
img {
    max-width: 100%;
    height: auto;
    loading: lazy;
    decoding: async;
}

/* Critical images load immediately */
.hero-image, .above-fold-image {
    loading: eager !important;
}

/* Font loading optimization */
@font-face {
    font-family: 'Inter';
    font-display: swap;
    font-swap-period: 100ms;
}

/* Skip links for accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #0ea5e9;
    color: white;
    padding: 8px;
    z-index: 1000;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 600;
}

.skip-link:focus {
    top: 6px;
}

/* Container styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Grid system for layout */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-8 {
    gap: 2rem;
}

.gap-16 {
    gap: 4rem;
}

/* Flexbox utilities */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

/* Text styles */
.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
}

.text-6xl {
    font-size: 3.75rem;
    line-height: 1;
}

.text-white {
    color: #ffffff;
}

.text-gray-300 {
    color: #d1d5db;
}

.font-bold {
    font-weight: 700;
}

.font-extrabold {
    font-weight: 800;
}

/* Spacing utilities */
.pt-20 {
    padding-top: 5rem;
}

.pb-32 {
    padding-bottom: 8rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

/* Background utilities */
.bg-slate-950 {
    background-color: #020617;
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* Position utilities */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

/* Z-index utilities */
.z-0 {
    z-index: 0;
}

.z-10 {
    z-index: 10;
}

/* Overflow utilities */
.overflow-hidden {
    overflow: hidden;
}

/* Responsive design */
@media (min-width: 768px) {
    .md\\:text-6xl {
        font-size: 3.75rem;
        line-height: 1;
    }
    
    .md\\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .lg\\:text-7xl {
        font-size: 4.5rem;
        line-height: 1;
    }
    
    .lg\\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    body {
        scroll-behavior: auto;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .skip-link {
        background: #000000;
        border: 2px solid #ffffff;
    }
}

/* Loading states to prevent layout shift */
.loading-placeholder {
    background: linear-gradient(90deg, #334155 25%, #475569 50%, #334155 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
