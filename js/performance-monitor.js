/**
 * Comprehensive Performance Monitor
 * Tracks Core Web Vitals and provides real-time performance insights
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            lcp: null,
            fid: null,
            cls: null,
            fcp: null,
            ttfb: null,
            inp: null
        };
        this.observers = [];
        this.isMonitoring = false;
        this.init();
    }

    init() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.setupCoreWebVitalsMonitoring();
        this.setupCustomMetrics();
        this.setupPerformanceObserver();
        this.scheduleReporting();
    }

    setupCoreWebVitalsMonitoring() {
        // Largest Contentful Paint (LCP)
        this.observeLCP();
        
        // First Input Delay (FID) / Interaction to Next Paint (INP)
        this.observeINP();
        
        // Cumulative Layout Shift (CLS)
        this.observeCLS();
        
        // First Contentful Paint (FCP)
        this.observeFCP();
        
        // Time to First Byte (TTFB)
        this.observeTTFB();
    }

    observeLCP() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                
                this.metrics.lcp = {
                    value: lastEntry.startTime,
                    element: lastEntry.element,
                    timestamp: Date.now(),
                    rating: this.getRating('lcp', lastEntry.startTime)
                };
                
                this.reportMetric('lcp', this.metrics.lcp);
            });
            
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
            this.observers.push(observer);
        }
    }

    observeINP() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.processingStart && entry.startTime) {
                        const inp = entry.processingStart - entry.startTime;
                        
                        if (!this.metrics.inp || inp > this.metrics.inp.value) {
                            this.metrics.inp = {
                                value: inp,
                                timestamp: Date.now(),
                                rating: this.getRating('inp', inp)
                            };
                        }
                    }
                }
                
                if (this.metrics.inp) {
                    this.reportMetric('inp', this.metrics.inp);
                }
            });
            
            observer.observe({ entryTypes: ['event'] });
            this.observers.push(observer);
        }
    }

    observeCLS() {
        if ('PerformanceObserver' in window) {
            let clsValue = 0;
            let sessionValue = 0;
            let sessionEntries = [];
            
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        const firstSessionEntry = sessionEntries[0];
                        const lastSessionEntry = sessionEntries[sessionEntries.length - 1];
                        
                        if (sessionValue && 
                            entry.startTime - lastSessionEntry.startTime < 1000 &&
                            entry.startTime - firstSessionEntry.startTime < 5000) {
                            sessionValue += entry.value;
                            sessionEntries.push(entry);
                        } else {
                            sessionValue = entry.value;
                            sessionEntries = [entry];
                        }
                        
                        if (sessionValue > clsValue) {
                            clsValue = sessionValue;
                            
                            this.metrics.cls = {
                                value: clsValue,
                                entries: [...sessionEntries],
                                timestamp: Date.now(),
                                rating: this.getRating('cls', clsValue)
                            };
                            
                            this.reportMetric('cls', this.metrics.cls);
                        }
                    }
                }
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
            this.observers.push(observer);
        }
    }

    observeFCP() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name === 'first-contentful-paint') {
                        this.metrics.fcp = {
                            value: entry.startTime,
                            timestamp: Date.now(),
                            rating: this.getRating('fcp', entry.startTime)
                        };
                        
                        this.reportMetric('fcp', this.metrics.fcp);
                        observer.disconnect();
                        break;
                    }
                }
            });
            
            observer.observe({ entryTypes: ['paint'] });
            this.observers.push(observer);
        }
    }

    observeTTFB() {
        if ('performance' in window && 'timing' in performance) {
            const navigationEntry = performance.getEntriesByType('navigation')[0];
            
            if (navigationEntry) {
                const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
                
                this.metrics.ttfb = {
                    value: ttfb,
                    timestamp: Date.now(),
                    rating: this.getRating('ttfb', ttfb)
                };
                
                this.reportMetric('ttfb', this.metrics.ttfb);
            }
        }
    }

    setupCustomMetrics() {
        // Monitor resource loading
        this.monitorResourceLoading();
        
        // Monitor JavaScript errors
        this.monitorErrors();
        
        // Monitor memory usage
        this.monitorMemoryUsage();
    }

    monitorResourceLoading() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const resources = performance.getEntriesByType('resource');
                const analysis = this.analyzeResources(resources);
                
                this.reportCustomMetric('resource-analysis', analysis);
            }, 1000);
        });
    }

    analyzeResources(resources) {
        const analysis = {
            totalResources: resources.length,
            totalSize: 0,
            slowestResource: null,
            resourceTypes: {},
            renderBlockingResources: []
        };

        resources.forEach(resource => {
            // Calculate total size
            analysis.totalSize += resource.transferSize || 0;
            
            // Track resource types
            const type = this.getResourceType(resource.name);
            analysis.resourceTypes[type] = (analysis.resourceTypes[type] || 0) + 1;
            
            // Find slowest resource
            if (!analysis.slowestResource || resource.duration > analysis.slowestResource.duration) {
                analysis.slowestResource = {
                    name: resource.name,
                    duration: resource.duration,
                    size: resource.transferSize
                };
            }
            
            // Identify render-blocking resources
            if (this.isRenderBlocking(resource)) {
                analysis.renderBlockingResources.push({
                    name: resource.name,
                    duration: resource.duration
                });
            }
        });

        return analysis;
    }

    getResourceType(url) {
        if (url.endsWith('.css')) return 'css';
        if (url.endsWith('.js')) return 'js';
        if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
        if (url.match(/\.(woff|woff2|ttf|otf)$/)) return 'font';
        return 'other';
    }

    isRenderBlocking(resource) {
        const url = resource.name.toLowerCase();
        return (url.endsWith('.css') && !url.includes('async')) ||
               (url.endsWith('.js') && !url.includes('defer') && !url.includes('async'));
    }

    monitorErrors() {
        window.addEventListener('error', (event) => {
            this.reportCustomMetric('javascript-error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: Date.now()
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.reportCustomMetric('promise-rejection', {
                reason: event.reason,
                timestamp: Date.now()
            });
        });
    }

    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.reportCustomMetric('memory-usage', {
                    usedJSHeapSize: memory.usedJSHeapSize,
                    totalJSHeapSize: memory.totalJSHeapSize,
                    jsHeapSizeLimit: memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                });
            }, 30000); // Every 30 seconds
        }
    }

    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'measure') {
                        this.reportCustomMetric('custom-measure', {
                            name: entry.name,
                            duration: entry.duration,
                            timestamp: Date.now()
                        });
                    }
                }
            });
            
            observer.observe({ entryTypes: ['measure'] });
            this.observers.push(observer);
        }
    }

    getRating(metric, value) {
        const thresholds = {
            lcp: { good: 2500, poor: 4000 },
            fid: { good: 100, poor: 300 },
            cls: { good: 0.1, poor: 0.25 },
            fcp: { good: 1800, poor: 3000 },
            ttfb: { good: 800, poor: 1800 },
            inp: { good: 200, poor: 500 }
        };

        const threshold = thresholds[metric];
        if (!threshold) return 'unknown';

        if (value <= threshold.good) return 'good';
        if (value <= threshold.poor) return 'needs-improvement';
        return 'poor';
    }

    reportMetric(name, data) {
        console.log(`Core Web Vital - ${name.toUpperCase()}:`, data);
        
        // Send to analytics
        if ('gtag' in window) {
            gtag('event', name, {
                event_category: 'Web Vitals',
                value: Math.round(data.value),
                custom_parameter_rating: data.rating
            });
        }
        
        // Mark in performance timeline
        if ('performance' in window && 'mark' in performance) {
            performance.mark(`${name}-${data.rating}-${Math.round(data.value)}`);
        }
    }

    reportCustomMetric(name, data) {
        console.log(`Custom Metric - ${name}:`, data);
        
        // Send to analytics if needed
        if ('gtag' in window && name === 'javascript-error') {
            gtag('event', 'exception', {
                description: data.message,
                fatal: false
            });
        }
    }

    scheduleReporting() {
        // Report metrics periodically
        setInterval(() => {
            this.generateReport();
        }, 60000); // Every minute

        // Report on page unload
        window.addEventListener('beforeunload', () => {
            this.generateReport();
        });
    }

    generateReport() {
        const report = {
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            metrics: { ...this.metrics },
            performance: this.getPerformanceSummary()
        };

        console.log('Performance Report:', report);
        
        // Send to your analytics endpoint
        this.sendToAnalytics(report);
    }

    getPerformanceSummary() {
        const navigation = performance.getEntriesByType('navigation')[0];
        
        return {
            domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.navigationStart : null,
            loadComplete: navigation ? navigation.loadEventEnd - navigation.navigationStart : null,
            resourceCount: performance.getEntriesByType('resource').length
        };
    }

    sendToAnalytics(report) {
        // Implement your analytics sending logic here
        // This could be to Google Analytics, your own endpoint, etc.

        try {
            if (navigator.sendBeacon && window.location.hostname !== 'test.x-zoneservers.com') {
                const data = JSON.stringify(report);
                // Only send to actual analytics endpoint, not 404.html
                const success = navigator.sendBeacon('/api/analytics/performance', data);
                if (!success) {
                    console.log('Beacon failed, using fetch fallback');
                    this.sendViaFetch(report);
                }
            } else {
                console.log('Performance analytics disabled for test environment');
            }
        } catch (error) {
            console.log('Analytics sending failed:', error);
        }
    }

    sendViaFetch(report) {
        fetch('/api/analytics/performance', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(report)
        }).catch(error => {
            console.log('Fetch analytics failed:', error);
        });
    }

    // Public methods for manual testing
    getMetrics() {
        return { ...this.metrics };
    }

    measureCustom(name, fn) {
        const startMark = `${name}-start`;
        const endMark = `${name}-end`;
        
        performance.mark(startMark);
        const result = fn();
        performance.mark(endMark);
        performance.measure(name, startMark, endMark);
        
        return result;
    }

    disconnect() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers = [];
        this.isMonitoring = false;
    }
}

// Initialize performance monitoring
const performanceMonitor = new PerformanceMonitor();

// Export for use in other scripts
window.performanceMonitor = performanceMonitor;

// Add debugging utilities
window.debugPerformance = {
    getMetrics: () => performanceMonitor.getMetrics(),
    generateReport: () => performanceMonitor.generateReport(),
    measureCustom: (name, fn) => performanceMonitor.measureCustom(name, fn)
};
