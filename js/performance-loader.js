/**
 * Performance-Optimized Script Loader
 * Implements intelligent loading strategies for better Core Web Vitals
 */

class PerformanceLoader {
    constructor() {
        this.loadedScripts = new Set();
        this.criticalScripts = [
            'js/header-loader.js',
            'js/footer-loader.js'
        ];
        this.performanceScripts = [
            'js/performance-monitor.js',
            'js/advanced-performance-monitor.js',
            'js/next-gen-core-web-vitals.js'
        ];
        this.seoScripts = [
            'js/optimized-image-loader.js',
            'js/layout-shift-prevention.js',
            'js/image-seo-optimization.js',
            'js/ai-sge-optimization.js',
            'js/semantic-seo-clusters.js',
            'js/google-2025-experimental-seo.js',
            'js/geographic-seo-optimizer.js',
            'js/core-web-vitals-dashboard.js',
            'js/advanced-seo-analytics.js',
            'js/ai-personalization-schema.js'
        ];
        this.init();
    }

    init() {
        // Load critical scripts immediately
        this.loadCriticalScripts();
        
        // Load performance scripts after DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.loadPerformanceScripts();
            });
        } else {
            this.loadPerformanceScripts();
        }
        
        // Load SEO scripts after page load
        window.addEventListener('load', () => {
            // Use requestIdleCallback for non-critical scripts
            if ('requestIdleCallback' in window) {
                requestIdleCallback(() => this.loadSEOScripts(), { timeout: 2000 });
            } else {
                setTimeout(() => this.loadSEOScripts(), 1000);
            }
        });
    }

    loadScript(src, options = {}) {
        return new Promise((resolve, reject) => {
            if (this.loadedScripts.has(src)) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.async = options.async !== false;
            script.defer = options.defer === true;
            
            script.onload = () => {
                this.loadedScripts.add(src);
                resolve();
            };
            
            script.onerror = () => {
                console.warn(`Failed to load script: ${src}`);
                reject(new Error(`Script load failed: ${src}`));
            };

            document.head.appendChild(script);
        });
    }

    async loadCriticalScripts() {
        try {
            await Promise.all(
                this.criticalScripts.map(script => 
                    this.loadScript(script, { async: false })
                )
            );
        } catch (error) {
            console.warn('Some critical scripts failed to load:', error);
        }
    }

    async loadPerformanceScripts() {
        try {
            await Promise.all(
                this.performanceScripts.map(script => 
                    this.loadScript(script, { defer: true })
                )
            );
        } catch (error) {
            console.warn('Some performance scripts failed to load:', error);
        }
    }

    async loadSEOScripts() {
        // Load SEO scripts one by one to avoid overwhelming the browser
        for (const script of this.seoScripts) {
            try {
                await this.loadScript(script, { defer: true });
                // Small delay between scripts to prevent blocking
                await new Promise(resolve => setTimeout(resolve, 50));
            } catch (error) {
                console.warn(`SEO script failed to load: ${script}`, error);
            }
        }
    }

    // Method to load additional scripts on demand
    loadOnDemand(scriptSrc) {
        return this.loadScript(scriptSrc, { async: true });
    }
}

// Initialize the performance loader
const performanceLoader = new PerformanceLoader();

// Export for use in other scripts
window.performanceLoader = performanceLoader;

// Performance monitoring for script loading
if ('performance' in window && 'getEntriesByType' in performance) {
    window.addEventListener('load', () => {
        setTimeout(() => {
            const resources = performance.getEntriesByType('resource');
            const scripts = resources.filter(resource => resource.name.endsWith('.js'));
            
            console.log('Script loading performance:', {
                totalScripts: scripts.length,
                averageLoadTime: scripts.reduce((sum, script) => sum + script.duration, 0) / scripts.length,
                slowestScript: scripts.reduce((slowest, script) => 
                    script.duration > slowest.duration ? script : slowest
                )
            });
        }, 1000);
    });
}

// Preload critical resources
const preloadResources = [
    { href: 'https://cdn.tailwindcss.com', as: 'script' },
    { href: 'https://unpkg.com/lucide@latest/dist/umd/lucide.js', as: 'script' },
    { href: 'style.css', as: 'style' }
];

preloadResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as;
    if (resource.as === 'script') {
        link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
});

// Implement resource hints for better performance
const resourceHints = [
    { rel: 'dns-prefetch', href: '//cdn.tailwindcss.com' },
    { rel: 'dns-prefetch', href: '//unpkg.com' },
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
    { rel: 'preconnect', href: 'https://fonts.googleapis.com', crossOrigin: true },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossOrigin: true }
];

resourceHints.forEach(hint => {
    const link = document.createElement('link');
    link.rel = hint.rel;
    link.href = hint.href;
    if (hint.crossOrigin) {
        link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
});

// Service Worker registration for caching
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Intersection Observer for lazy loading
const createIntersectionObserver = (callback, options = {}) => {
    const defaultOptions = {
        root: null,
        rootMargin: '50px',
        threshold: 0.1
    };
    
    return new IntersectionObserver(callback, { ...defaultOptions, ...options });
};

// Export utilities
window.createIntersectionObserver = createIntersectionObserver;

// Performance budget monitoring
const performanceBudget = {
    maxScripts: 15,
    maxTotalSize: 500 * 1024, // 500KB
    maxLoadTime: 3000 // 3 seconds
};

window.addEventListener('load', () => {
    setTimeout(() => {
        const resources = performance.getEntriesByType('resource');
        const scripts = resources.filter(r => r.name.endsWith('.js'));
        const totalSize = scripts.reduce((sum, script) => sum + (script.transferSize || 0), 0);
        const maxLoadTime = Math.max(...scripts.map(s => s.responseEnd - s.startTime));
        
        if (scripts.length > performanceBudget.maxScripts) {
            console.warn(`Performance budget exceeded: ${scripts.length} scripts (max: ${performanceBudget.maxScripts})`);
        }
        
        if (totalSize > performanceBudget.maxTotalSize) {
            console.warn(`Performance budget exceeded: ${Math.round(totalSize/1024)}KB total size (max: ${performanceBudget.maxTotalSize/1024}KB)`);
        }
        
        if (maxLoadTime > performanceBudget.maxLoadTime) {
            console.warn(`Performance budget exceeded: ${Math.round(maxLoadTime)}ms max load time (max: ${performanceBudget.maxLoadTime}ms)`);
        }
    }, 2000);
});
