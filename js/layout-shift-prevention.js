/**
 * Layout Shift Prevention for Core Web Vitals
 * Prevents Cumulative Layout Shift (CLS) issues
 */

class LayoutShiftPrevention {
    constructor() {
        this.observer = null;
        this.reservedSpaces = new Map();
        this.init();
    }

    init() {
        // Reserve space for dynamic content
        this.reserveSpaceForDynamicContent();
        
        // Fix image dimensions
        this.fixImageDimensions();
        
        // Optimize font loading
        this.optimizeFontLoading();
        
        // Monitor layout shifts
        this.monitorLayoutShifts();
        
        // Handle dynamic content loading
        this.handleDynamicContent();
    }

    reserveSpaceForDynamicContent() {
        // Reserve space for header
        const headerPlaceholder = document.getElementById('header-placeholder');
        if (headerPlaceholder && !headerPlaceholder.style.height) {
            headerPlaceholder.style.height = '80px';
            headerPlaceholder.style.backgroundColor = 'rgba(15, 23, 42, 0.95)';
            headerPlaceholder.style.backdropFilter = 'blur(10px)';
        }

        // Reserve space for footer
        const footerPlaceholder = document.getElementById('footer-placeholder');
        if (footerPlaceholder && !footerPlaceholder.style.minHeight) {
            footerPlaceholder.style.minHeight = '400px';
            footerPlaceholder.style.backgroundColor = 'rgba(15, 23, 42, 0.8)';
        }

        // Reserve space for common dynamic elements
        this.reserveSpaceForElements([
            { selector: '.loading-content', height: '200px' },
            { selector: '.dynamic-stats', height: '120px' },
            { selector: '.testimonial-slider', height: '300px' },
            { selector: '.pricing-cards', height: '400px' }
        ]);
    }

    reserveSpaceForElements(elements) {
        elements.forEach(({ selector, height, width }) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (!element.style.minHeight && height) {
                    element.style.minHeight = height;
                }
                if (!element.style.minWidth && width) {
                    element.style.minWidth = width;
                }
                
                // Add loading placeholder
                if (!element.classList.contains('has-placeholder')) {
                    element.classList.add('loading-placeholder');
                    element.classList.add('has-placeholder');
                }
            });
        });
    }

    fixImageDimensions() {
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            // Skip if already has dimensions
            if (img.style.width || img.style.height || img.width || img.height) {
                return;
            }

            // Try to get dimensions from attributes or CSS
            const width = img.getAttribute('width') || img.naturalWidth;
            const height = img.getAttribute('height') || img.naturalHeight;

            if (width && height) {
                // Set aspect ratio to prevent layout shift
                const aspectRatio = height / width;
                img.style.aspectRatio = `${width} / ${height}`;
                img.style.width = '100%';
                img.style.height = 'auto';
            } else {
                // Set default dimensions for unknown images
                img.style.minHeight = '200px';
                img.style.backgroundColor = '#334155';
                
                // Try to determine dimensions when image loads
                img.addEventListener('load', () => {
                    if (img.naturalWidth && img.naturalHeight) {
                        const aspectRatio = img.naturalHeight / img.naturalWidth;
                        img.style.aspectRatio = `${img.naturalWidth} / ${img.naturalHeight}`;
                        img.style.minHeight = '';
                        img.style.backgroundColor = '';
                    }
                }, { once: true });
            }
        });
    }

    optimizeFontLoading() {
        // Preload critical fonts
        const criticalFonts = [
            {
                family: 'Inter',
                weights: ['400', '500', '600', '700', '900'],
                display: 'swap'
            }
        ];

        criticalFonts.forEach(font => {
            font.weights.forEach(weight => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'font';
                link.type = 'font/woff2';
                link.crossOrigin = 'anonymous';
                link.href = `https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2`;
                document.head.appendChild(link);
            });
        });

        // Add font-display: swap to existing font faces
        const style = document.createElement('style');
        style.textContent = `
            @font-face {
                font-family: 'Inter';
                font-display: swap;
                font-swap-period: 100ms;
            }
        `;
        document.head.appendChild(style);
    }

    monitorLayoutShifts() {
        if ('LayoutShift' in window && 'PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                let clsScore = 0;
                
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        clsScore += entry.value;
                    }
                }
                
                if (clsScore > 0.1) {
                    console.warn(`Layout shift detected: ${clsScore.toFixed(4)}`);
                    
                    // Report to analytics or monitoring service
                    this.reportLayoutShift(clsScore);
                }
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
        }
    }

    reportLayoutShift(score) {
        // Report to your analytics service
        if ('gtag' in window) {
            gtag('event', 'layout_shift', {
                event_category: 'Web Vitals',
                event_label: 'CLS',
                value: Math.round(score * 1000)
            });
        }
        
        // Mark in performance timeline
        if ('performance' in window && 'mark' in performance) {
            performance.mark(`layout-shift-${score.toFixed(4)}`);
        }
    }

    handleDynamicContent() {
        // Monitor for content changes that might cause layout shifts
        const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.preventShiftForNewElement(node);
                        }
                    });
                }
            });
        });

        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    preventShiftForNewElement(element) {
        // Add dimensions to prevent layout shift
        if (element.tagName === 'IMG') {
            this.fixImageDimensions();
        }
        
        // Handle common dynamic elements
        if (element.classList.contains('dynamic-content')) {
            element.style.minHeight = '100px';
        }
        
        // Handle ads or embedded content
        if (element.classList.contains('ad-container') || 
            element.tagName === 'IFRAME') {
            const width = element.getAttribute('width') || '300';
            const height = element.getAttribute('height') || '250';
            element.style.width = width + 'px';
            element.style.height = height + 'px';
        }
    }

    // Method to manually reserve space for an element
    reserveSpace(selector, dimensions) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (dimensions.width) {
                element.style.minWidth = dimensions.width;
            }
            if (dimensions.height) {
                element.style.minHeight = dimensions.height;
            }
            
            this.reservedSpaces.set(element, dimensions);
        });
    }

    // Method to release reserved space
    releaseSpace(element) {
        if (this.reservedSpaces.has(element)) {
            element.style.minWidth = '';
            element.style.minHeight = '';
            this.reservedSpaces.delete(element);
        }
    }

    // Get current CLS score
    getCurrentCLS() {
        return new Promise((resolve) => {
            if ('LayoutShift' in window && 'PerformanceObserver' in window) {
                let clsScore = 0;
                
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (!entry.hadRecentInput) {
                            clsScore += entry.value;
                        }
                    }
                    
                    observer.disconnect();
                    resolve(clsScore);
                });
                
                observer.observe({ entryTypes: ['layout-shift'] });
                
                // Timeout after 1 second
                setTimeout(() => {
                    observer.disconnect();
                    resolve(clsScore);
                }, 1000);
            } else {
                resolve(0);
            }
        });
    }
}

// Add CSS for loading placeholders
const placeholderStyle = document.createElement('style');
placeholderStyle.textContent = `
    .loading-placeholder {
        background: linear-gradient(90deg, #334155 25%, #475569 50%, #334155 75%);
        background-size: 200% 100%;
        animation: loading-shimmer 1.5s infinite;
        border-radius: 0.5rem;
    }
    
    @keyframes loading-shimmer {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    /* Prevent layout shift for common elements */
    .hero-section {
        min-height: 600px;
    }
    
    .stats-section {
        min-height: 200px;
    }
    
    .testimonials {
        min-height: 300px;
    }
    
    /* Aspect ratio containers */
    .aspect-ratio-16-9 {
        aspect-ratio: 16 / 9;
    }
    
    .aspect-ratio-4-3 {
        aspect-ratio: 4 / 3;
    }
    
    .aspect-ratio-1-1 {
        aspect-ratio: 1 / 1;
    }
`;
document.head.appendChild(placeholderStyle);

// Initialize layout shift prevention
const layoutShiftPrevention = new LayoutShiftPrevention();

// Export for use in other scripts
window.layoutShiftPrevention = layoutShiftPrevention;

// Performance monitoring
window.addEventListener('load', () => {
    setTimeout(async () => {
        const clsScore = await layoutShiftPrevention.getCurrentCLS();
        console.log(`Current CLS score: ${clsScore.toFixed(4)}`);
        
        if (clsScore > 0.1) {
            console.warn('CLS score exceeds recommended threshold of 0.1');
        }
    }, 3000);
});
