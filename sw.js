/**
 * Service Worker for X-ZoneServers
 * Optimized for Core Web Vitals and performance
 */

const CACHE_NAME = 'x-zoneservers-v1.2';
const STATIC_CACHE = 'static-v1.2';
const DYNAMIC_CACHE = 'dynamic-v1.2';

// Resources to cache immediately
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/dedicated.html',
    '/style.css',
    '/critical.css',
    '/js/header-loader.js',
    '/js/footer-loader.js',
    '/js/performance-loader.js',
    '/js/advanced-performance-monitor.js',
    '/js/next-gen-core-web-vitals.js'
];

// External resources to cache
const EXTERNAL_ASSETS = [
    'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap',
    'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2'
];

// Cache strategies
const CACHE_STRATEGIES = {
    CACHE_FIRST: 'cache-first',
    NETWORK_FIRST: 'network-first',
    STALE_WHILE_REVALIDATE: 'stale-while-revalidate'
};

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Caching static assets...');
                return cache.addAll(STATIC_ASSETS);
            }),
            caches.open(DYNAMIC_CACHE).then(cache => {
                console.log('Pre-caching external assets...');
                return cache.addAll(EXTERNAL_ASSETS.slice(0, 5)); // Cache first 5 to avoid overwhelming
            })
        ]).then(() => {
            console.log('Service Worker installed successfully');
            return self.skipWaiting();
        }).catch(error => {
            console.error('Service Worker installation failed:', error);
        })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker activated');
            return self.clients.claim();
        })
    );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Skip chrome-extension and other non-http requests
    if (!url.protocol.startsWith('http')) {
        return;
    }

    // Skip requests that might cause issues
    if (url.pathname.includes('404.html') ||
        url.pathname.includes('beacon') ||
        request.method === 'POST' ||
        url.hostname.includes('test.x-zoneservers.com') ||
        url.pathname.endsWith('/404.html/')) {
        return;
    }

    // Determine cache strategy based on resource type
    let strategy = CACHE_STRATEGIES.STALE_WHILE_REVALIDATE;

    if (isStaticAsset(url)) {
        strategy = CACHE_STRATEGIES.CACHE_FIRST;
    } else if (isAPIRequest(url)) {
        strategy = CACHE_STRATEGIES.NETWORK_FIRST;
    }

    event.respondWith(handleRequest(request, strategy).catch(error => {
        console.error('Service worker fetch error:', error);
        return fetch(request); // Fallback to network
    }));
});

// Check if URL is a static asset
function isStaticAsset(url) {
    const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.woff', '.woff2', '.ttf'];
    const pathname = url.pathname.toLowerCase();
    
    return staticExtensions.some(ext => pathname.endsWith(ext)) ||
           url.hostname === 'fonts.googleapis.com' ||
           url.hostname === 'fonts.gstatic.com' ||
           url.hostname === 'cdn.tailwindcss.com';
}

// Check if URL is an API request
function isAPIRequest(url) {
    return url.pathname.includes('/api/') || 
           url.hostname === 'ipapi.co' ||
           url.pathname.includes('.json');
}

// Handle requests based on strategy
async function handleRequest(request, strategy) {
    const cacheName = isStaticAsset(new URL(request.url)) ? STATIC_CACHE : DYNAMIC_CACHE;

    try {
        switch (strategy) {
            case CACHE_STRATEGIES.CACHE_FIRST:
                return await cacheFirst(request, cacheName);

            case CACHE_STRATEGIES.NETWORK_FIRST:
                return await networkFirst(request, cacheName);

            case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
            default:
                return await staleWhileRevalidate(request, cacheName);
        }
    } catch (error) {
        console.error('Handle request error:', error);
        // Always return a valid response
        return await fetch(request).catch(() => {
            return new Response('Service Unavailable', {
                status: 503,
                statusText: 'Service Unavailable',
                headers: { 'Content-Type': 'text/plain' }
            });
        });
    }
}

// Cache first strategy
async function cacheFirst(request, cacheName) {
    try {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Cache first strategy failed:', error);
        return new Response('Offline', { status: 503 });
    }
}

// Network first strategy
async function networkFirst(request, cacheName) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(cacheName);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', error);
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return new Response('Offline', { status: 503 });
    }
}

// Stale while revalidate strategy
async function staleWhileRevalidate(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);

    // Fetch from network in background
    const networkResponsePromise = fetch(request).then(response => {
        if (response && response.ok) {
            cache.put(request, response.clone());
        }
        return response;
    }).catch(error => {
        console.log('Background fetch failed:', error);
        return null;
    });

    // Return cached response immediately if available
    if (cachedResponse) {
        return cachedResponse;
    }

    // If no cached response, wait for network
    try {
        const networkResponse = await networkResponsePromise;
        if (networkResponse && networkResponse.ok) {
            return networkResponse;
        }
        throw new Error('Network response not ok');
    } catch (error) {
        return new Response('Service Unavailable', {
            status: 503,
            statusText: 'Service Unavailable',
            headers: { 'Content-Type': 'text/plain' }
        });
    }
}

// Background sync for offline actions
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    // Implement background sync logic here
    console.log('Background sync triggered');
}

// Push notifications (if needed)
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey
            }
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    event.waitUntil(
        clients.openWindow('/')
    );
});

// Performance monitoring
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_CACHE_SIZE') {
        getCacheSize().then(size => {
            event.ports[0].postMessage({ cacheSize: size });
        });
    }
});

async function getCacheSize() {
    const cacheNames = await caches.keys();
    let totalSize = 0;
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
            const response = await cache.match(request);
            if (response) {
                const blob = await response.blob();
                totalSize += blob.size;
            }
        }
    }
    
    return totalSize;
}
