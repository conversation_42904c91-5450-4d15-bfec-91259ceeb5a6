/**
 * Optimized Image Loader for Core Web Vitals
 * Implements advanced lazy loading and image optimization
 */

class OptimizedImageLoader {
    constructor() {
        this.observer = null;
        this.loadedImages = new Set();
        this.imageQueue = [];
        this.isProcessing = false;
        this.init();
    }

    init() {
        // Create intersection observer for lazy loading
        this.createObserver();
        
        // Process existing images
        this.processExistingImages();
        
        // Monitor for new images added dynamically
        this.observeNewImages();
        
        // Preload critical images
        this.preloadCriticalImages();
    }

    createObserver() {
        const options = {
            root: null,
            rootMargin: '50px 0px',
            threshold: 0.01
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.observer.unobserve(entry.target);
                }
            });
        }, options);
    }

    processExistingImages() {
        const images = document.querySelectorAll('img[data-src], img[loading="lazy"]');
        images.forEach(img => this.setupImage(img));
    }

    setupImage(img) {
        // Skip if already processed
        if (this.loadedImages.has(img)) return;

        // Add placeholder for layout stability
        this.addPlaceholder(img);
        
        // Set up responsive images
        this.setupResponsiveImage(img);
        
        // Determine loading strategy
        if (this.isCriticalImage(img)) {
            this.loadImage(img);
        } else {
            this.observer.observe(img);
        }
    }

    addPlaceholder(img) {
        if (!img.style.backgroundColor && !img.classList.contains('has-placeholder')) {
            // Add a subtle placeholder to prevent layout shift
            img.style.backgroundColor = '#334155';
            img.style.minHeight = img.getAttribute('height') || '200px';
            img.classList.add('has-placeholder');
            
            // Add loading animation
            img.style.backgroundImage = `
                linear-gradient(90deg, 
                    rgba(51, 65, 85, 0.2) 25%, 
                    rgba(71, 85, 105, 0.4) 50%, 
                    rgba(51, 65, 85, 0.2) 75%)
            `;
            img.style.backgroundSize = '200% 100%';
            img.style.animation = 'loading-shimmer 1.5s infinite';
        }
    }

    setupResponsiveImage(img) {
        const src = img.getAttribute('data-src') || img.src;
        if (!src) return;

        // Generate responsive image URLs (assuming a CDN or image service)
        const sizes = [320, 640, 768, 1024, 1280, 1920];
        const srcset = sizes.map(size => {
            const optimizedSrc = this.getOptimizedImageUrl(src, size);
            return `${optimizedSrc} ${size}w`;
        }).join(', ');

        if (srcset) {
            img.setAttribute('data-srcset', srcset);
            img.setAttribute('sizes', '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw');
        }
    }

    getOptimizedImageUrl(src, width) {
        // This would integrate with your image optimization service
        // For now, return the original URL
        // In production, you might use something like:
        // return `https://your-cdn.com/resize?url=${encodeURIComponent(src)}&w=${width}&format=webp&quality=80`;
        return src;
    }

    isCriticalImage(img) {
        // Check if image is above the fold or marked as critical
        const rect = img.getBoundingClientRect();
        const isAboveFold = rect.top < window.innerHeight;
        const isCritical = img.classList.contains('critical') || 
                          img.classList.contains('hero-image') ||
                          img.classList.contains('above-fold-image');
        
        return isAboveFold || isCritical;
    }

    async loadImage(img) {
        if (this.loadedImages.has(img)) return;

        const src = img.getAttribute('data-src') || img.src;
        const srcset = img.getAttribute('data-srcset');

        if (!src) return;

        try {
            // Create a new image to preload
            const newImg = new Image();
            
            // Set up the new image
            if (srcset) {
                newImg.srcset = srcset;
                newImg.sizes = img.getAttribute('sizes') || '100vw';
            }
            newImg.src = src;

            // Wait for image to load
            await new Promise((resolve, reject) => {
                newImg.onload = resolve;
                newImg.onerror = reject;
                
                // Timeout after 10 seconds
                setTimeout(reject, 10000);
            });

            // Apply the loaded image
            if (srcset) {
                img.srcset = srcset;
                img.sizes = newImg.sizes;
            }
            img.src = src;
            
            // Remove placeholder styling
            this.removePlaceholder(img);
            
            // Mark as loaded
            this.loadedImages.add(img);
            
            // Trigger load event
            img.dispatchEvent(new Event('imageLoaded'));

        } catch (error) {
            console.warn('Failed to load image:', src, error);
            this.handleImageError(img);
        }
    }

    removePlaceholder(img) {
        img.style.backgroundColor = '';
        img.style.backgroundImage = '';
        img.style.animation = '';
        img.classList.remove('has-placeholder');
    }

    handleImageError(img) {
        // Set a fallback image or hide the image
        img.style.display = 'none';
        img.setAttribute('aria-hidden', 'true');
        
        // Log for monitoring
        if ('performance' in window && 'mark' in performance) {
            performance.mark('image-load-error');
        }
    }

    preloadCriticalImages() {
        // Preload hero images and other critical images
        const criticalImages = document.querySelectorAll('.hero-image, .critical, .above-fold-image');
        
        criticalImages.forEach(img => {
            const src = img.getAttribute('data-src') || img.src;
            if (src && !this.loadedImages.has(img)) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = src;
                document.head.appendChild(link);
            }
        });
    }

    observeNewImages() {
        // Use MutationObserver to watch for new images
        const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the node itself is an image
                        if (node.tagName === 'IMG') {
                            this.setupImage(node);
                        }
                        
                        // Check for images within the added node
                        const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                        images.forEach(img => this.setupImage(img));
                    }
                });
            });
        });

        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Method to manually trigger image loading (useful for carousels, etc.)
    loadImageNow(img) {
        if (this.observer) {
            this.observer.unobserve(img);
        }
        this.loadImage(img);
    }

    // Get loading statistics
    getStats() {
        const allImages = document.querySelectorAll('img');
        return {
            totalImages: allImages.length,
            loadedImages: this.loadedImages.size,
            pendingImages: allImages.length - this.loadedImages.size,
            loadingPercentage: Math.round((this.loadedImages.size / allImages.length) * 100)
        };
    }
}

// Add CSS for loading animation
const style = document.createElement('style');
style.textContent = `
    @keyframes loading-shimmer {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    img.has-placeholder {
        transition: opacity 0.3s ease-in-out;
    }
    
    img[data-src] {
        opacity: 0;
    }
    
    img[data-src].loaded {
        opacity: 1;
    }
`;
document.head.appendChild(style);

// Initialize the image loader
const optimizedImageLoader = new OptimizedImageLoader();

// Export for use in other scripts
window.optimizedImageLoader = optimizedImageLoader;

// Performance monitoring
window.addEventListener('load', () => {
    setTimeout(() => {
        const stats = optimizedImageLoader.getStats();
        console.log('Image loading stats:', stats);
        
        // Report to performance monitoring
        if ('performance' in window && 'mark' in performance) {
            performance.mark(`images-loaded-${stats.loadingPercentage}%`);
        }
    }, 2000);
});

// Expose methods for debugging
if (typeof window !== 'undefined') {
    window.debugImageLoader = {
        getStats: () => optimizedImageLoader.getStats(),
        loadAllImages: () => {
            document.querySelectorAll('img[data-src]').forEach(img => {
                optimizedImageLoader.loadImageNow(img);
            });
        }
    };
}
